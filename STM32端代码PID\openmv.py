import sensor, image, time
from machine import UART
import math

# Color threshold for blue (adjust as needed)
# LAB颜色空间：L(亮度), A(绿红色差), B(蓝黄色差)
blue_threshold = (0, 100, -128, 127, -128, -10)  # LAB color space for blue

# 可选的多个蓝色阈值，用于不同光照条件
blue_thresholds = [
    (0, 100, -128, 127, -128, -10),   # 标准蓝色
    (10, 80, -128, 127, -128, 0),     # 亮蓝色
    (0, 60, -128, 127, -128, -20)     # 深蓝色
]

# Initialize the camera sensor
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)  # 必须关闭自动增益以进行颜色跟踪
sensor.set_auto_whitebal(False)  # 必须关闭自动白平衡以进行颜色跟踪
clock = time.clock()

# Initialize UART for communication
uart = UART(3, 115200)  # Use UART(3) for OpenMV4 H7 Plus, OpenMV4 H7, OpenMV3 M7

# Image center coordinates
img_center_x = sensor.width() // 2  # Image center X
img_center_y = sensor.height() // 2  # Image center Y

# PID parameters
Kp = 0.6  # Proportional gain
Ki = 0.03 # Integral gain 0.01
Kd = 0.2  # Derivative gain 0.1

# PID variables
last_error_x = 0
integral_x = 0
last_error_y = 0
integral_y = 0


Servo_dx = 0
Servo_dy = 0
Servo_data = [0,0];
# Function to find the largest blob


HEADER = b'\xFF'  # 包头
FOOTER = b'\xFE'  # 包尾

def send_packet(data):
    # 将数据转换为字节数组
    data_bytes = bytes(data)

    # 拼接数据包：包头 + 数据 + 包尾
    packet = HEADER + data_bytes + FOOTER

    # 发送数据包
    uart.write(packet)


def find_max(blobs):
    max_size = 0
    for blob in blobs:
        if blob.pixels() > max_size:
            max_blob = blob
            max_size = blob.pixels()
    return max_blob

# Function to calculate PID output
def pid_control(error, last_error, integral, Kp, Ki, Kd):
    # Proportional term
    proportional = Kp * error

    # Integral term
    integral += error
    integral_term = Ki * integral

    # Derivative term
    derivative = Kd * (error - last_error)

    # PID output
    output = proportional + integral_term + derivative

    # Update last error
    last_error = error

    return output, last_error, integral

# Function to send control signal via UART (已移除，直接在主循环中处理)



# Main loop
while(True):
    img = sensor.snapshot()  # Capture an image

    # Detect blobs based on the blue threshold
    # 尝试多个阈值以提高识别率
    blobs = img.find_blobs(blue_thresholds, pixels_threshold=100, area_threshold=100)
    if blobs:
        max_blob = find_max(blobs)  # Find the largest blob

        # Draw a rectangle and crosshair on the largest blob
        img.draw_rectangle(max_blob.rect())
        img.draw_cross(max_blob.cx(), max_blob.cy())

        # Get the center coordinates of the largest blob
        blob_cx = max_blob.cx()
        blob_cy = max_blob.cy()

        # Calculate error (deviation from image center)
        # 正值表示目标在右侧/下方，负值表示目标在左侧/上方
        error_x = blob_cx - img_center_x  # 水平误差
        error_y = blob_cy - img_center_y  # 垂直误差

        # PID control for X axis
        output_x, last_error_x, integral_x = pid_control(error_x, last_error_x, integral_x, Kp, Ki, Kd)

        # PID control for Y axis
        output_y, last_error_y, integral_y = pid_control(error_y, last_error_y, integral_y, Kp, Ki, Kd)

        # 直接发送误差值，简化处理
        # 将误差值映射到合适的范围并加上偏移量128
        servo_x = int(max(-127, min(127, error_x * 0.5))) + 128
        servo_y = int(max(-127, min(127, error_y * 0.5))) + 128
        Servo_data[0] = servo_x
        Servo_data[1] = servo_y

        send_packet(Servo_data)
        # Print debug information
        print("Blob: ({}, {}), Size: {}, Error: ({}, {}), Servo: ({}, {})".format(
            blob_cx, blob_cy, max_blob.pixels(), error_x, error_y, servo_x, servo_y))
    else:
        send_packet([128,128])
        print('not found!')  # No blobs detected
