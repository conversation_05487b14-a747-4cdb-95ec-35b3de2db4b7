# 二维云台蓝色色块跟踪系统 - 测试调试指南

## 系统概述
本系统实现了OpenMV识别蓝色色块，并通过串口将坐标差发送给STM32，STM32控制舵机实现360度跟踪的功能。

## 硬件连接
1. **OpenMV与STM32连接**：
   - OpenMV UART3 (TX) → STM32 PB11 (RX)
   - OpenMV UART3 (RX) → STM32 PB10 (TX)
   - 共地连接

2. **舵机连接**：
   - 舵机1（水平）→ STM32 PA0
   - 舵机2（垂直）→ STM32 PA1

## 软件功能
### OpenMV端功能
1. **蓝色色块识别**：使用LAB颜色空间阈值 `(0, 100, -128, 127, -128, -10)`
2. **坐标差计算**：计算色块中心与图像中心的偏差
3. **数据发送**：通过串口发送误差值给STM32

### STM32端功能
1. **串口通信**：接收OpenMV发送的数据包
2. **PID控制**：使用优化的单环PID算法
3. **舵机控制**：支持360度连续转动

## 调试步骤

### 1. 颜色阈值调试
如果蓝色识别效果不好，可以调整OpenMV中的颜色阈值：
```python
# 在openmv.py中调整这个参数
blue_threshold = (0, 100, -128, 127, -128, -10)
```

### 2. PID参数调试
在STM32的main.c中调整PID参数：
```c
float Kp = 1.2;    // 比例系数，控制响应速度
float Ki = 0.02;   // 积分系数，消除稳态误差
float Kd = 0.3;    // 微分系数，减少超调
```

**调试建议**：
- 先调Kp：从小到大，直到系统有明显响应
- 再调Kd：减少震荡和超调
- 最后调Ki：消除稳态误差

### 3. 系统测试流程
1. **硬件检查**：确认所有连接正确
2. **OpenMV测试**：运行OpenMV代码，观察色块识别效果
3. **串口通信测试**：检查STM32是否正确接收数据
4. **舵机响应测试**：观察舵机是否正确响应
5. **跟踪性能测试**：测试不同速度的目标跟踪效果

## 常见问题及解决方案

### 1. 色块识别不准确
- 调整光照条件
- 修改颜色阈值参数
- 确保目标色块足够大且颜色纯正

### 2. 舵机响应过慢或过快
- 调整PID参数中的Kp值
- 检查舵机供电是否充足

### 3. 系统震荡
- 减小Kp值
- 增加Kd值
- 检查机械结构是否稳固

### 4. 跟踪精度不够
- 增加Ki值消除稳态误差
- 优化OpenMV的误差计算算法
- 检查舵机精度和机械间隙

## 性能优化建议
1. **提高帧率**：优化OpenMV图像处理算法
2. **减少延迟**：优化串口通信协议
3. **提高精度**：使用更高精度的舵机
4. **增强稳定性**：添加卡尔曼滤波算法

## 参数记录表
| 参数 | 初始值 | 调试后值 | 备注 |
|------|--------|----------|------|
| Kp | 1.2 | | 比例系数 |
| Ki | 0.02 | | 积分系数 |
| Kd | 0.3 | | 微分系数 |
| 蓝色阈值L | 0-100 | | 亮度范围 |
| 蓝色阈值A | -128-127 | | 绿红色差 |
| 蓝色阈值B | -128--10 | | 蓝黄色差 |
