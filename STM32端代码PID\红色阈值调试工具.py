# 红色阈值调试工具
# 用于调试和优化红色色块识别参数

import sensor, image, time

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)
clock = time.clock()

# 多种红色阈值供测试
red_thresholds = [
    # 格式：(L_min, L_max, A_min, A_max, B_min, B_max)
    # L: 亮度 (0-100)
    # A: 绿红色差 (-128到127，正值偏红)
    # B: 蓝黄色差 (-128到127)
    
    (30, 100, 15, 127, -128, 127),   # 标准红色 - 默认
    (20, 80, 10, 127, -128, 127),    # 暗红色
    (40, 100, 20, 127, -128, 127),   # 亮红色
    (25, 95, 12, 127, -128, 127),    # 中等红色
    (15, 85, 8, 127, -128, 127),     # 深红色
    (35, 100, 18, 127, -128, 127),   # 鲜红色
]

# RGB颜色阈值（备选方案）
red_thresholds_rgb = [
    (30, 100, 15, 127, 15, 127),     # RGB红色1
    (20, 100, 10, 127, 10, 127),     # RGB红色2
]

current_threshold = 0  # 当前使用的阈值索引

print("红色阈值调试工具")
print("按键说明：")
print("- 无按键：使用当前阈值")
print("- 程序会自动循环测试不同阈值")

while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 自动切换阈值进行测试（每5秒切换一次）
    if clock.fps() > 0:
        # 每150帧切换一次阈值（约5秒）
        frame_count = int(clock.avg() * 150)
        current_threshold = (frame_count // 150) % len(red_thresholds)
    
    # 使用当前阈值检测红色色块
    current_red_threshold = red_thresholds[current_threshold]
    blobs = img.find_blobs([current_red_threshold], pixels_threshold=100, area_threshold=100)
    
    # 在图像上显示当前阈值信息
    img.draw_string(5, 5, "Threshold %d" % current_threshold, color=(255, 255, 255))
    img.draw_string(5, 20, "L:%d-%d" % (current_red_threshold[0], current_red_threshold[1]), color=(255, 255, 255))
    img.draw_string(5, 35, "A:%d-%d" % (current_red_threshold[2], current_red_threshold[3]), color=(255, 255, 255))
    img.draw_string(5, 50, "B:%d-%d" % (current_red_threshold[4], current_red_threshold[5]), color=(255, 255, 255))
    
    if blobs:
        # 找到最大的色块
        max_blob = max(blobs, key=lambda b: b.pixels())
        
        # 绘制矩形和十字线
        img.draw_rectangle(max_blob.rect(), color=(0, 255, 0))
        img.draw_cross(max_blob.cx(), max_blob.cy(), color=(0, 255, 0))
        
        # 显示色块信息
        img.draw_string(5, 65, "Size: %d" % max_blob.pixels(), color=(0, 255, 0))
        img.draw_string(5, 80, "Pos: (%d,%d)" % (max_blob.cx(), max_blob.cy()), color=(0, 255, 0))
        
        print("阈值 %d: 找到红色色块 - 位置:(%d,%d), 大小:%d" % 
              (current_threshold, max_blob.cx(), max_blob.cy(), max_blob.pixels()))
    else:
        img.draw_string(5, 65, "No red blob", color=(255, 0, 0))
        print("阈值 %d: 未找到红色色块" % current_threshold)
    
    # 显示图像中心十字线
    img.draw_cross(img.width()//2, img.height()//2, color=(255, 255, 0))
    
    print("FPS: %.2f, 当前阈值: %d" % (clock.fps(), current_threshold))

# 使用说明：
# 1. 运行此程序，观察不同阈值下的识别效果
# 2. 找到识别效果最好的阈值编号
# 3. 将对应的阈值参数复制到主程序中
# 4. 可以根据实际情况微调参数

# 常见红色物体的阈值参考：
# - 红色纸张：(30, 100, 15, 127, -128, 127)
# - 红色塑料：(25, 95, 12, 127, -128, 127)
# - 红色布料：(20, 90, 10, 127, -128, 127)
# - 红色LED：(40, 100, 20, 127, -128, 127)

# 调试技巧：
# 1. 在不同光照条件下测试
# 2. 调整L值范围适应亮度变化
# 3. 调整A值下限提高红色选择性
# 4. B值通常保持较大范围
