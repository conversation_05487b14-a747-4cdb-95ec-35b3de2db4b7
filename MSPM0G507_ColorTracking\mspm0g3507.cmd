/*
 * MSPM0G3507 链接脚本文件
 * 定义内存布局和段分配
 */

MEMORY
{
    FLASH (RX)  : origin = 0x00000000, length = 0x00020000
    SRAM (RWX)  : origin = 0x20000000, length = 0x00008000
}

/* 段分配 */
SECTIONS
{
    .intvecs:   > 0x00000000
    .text   :   > FLASH
    .const  :   > FLASH
    .cinit  :   > FLASH
    .pinit  :   > FLASH
    .init_array : > FLASH

    .vtable :   > 0x20000000
    .data   :   > SRAM
    .bss    :   > SRAM
    .sysmem :   > SRAM
    .stack  :   > SRAM (HIGH)
}
