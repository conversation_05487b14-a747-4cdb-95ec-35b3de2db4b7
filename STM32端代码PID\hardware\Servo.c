#include "stm32f10x.h"                  // Device header
#include "PWM.h"
void Servo_Init(void)
{
	PWM_Init();
}

// 标准舵机控制函数（0-180度）
void Servo1_SetAngle(float Angle)
{
	// 限制角度范围到0-180度
	if(Angle < 0) Angle = 0;
	if(Angle > 180) Angle = 180;
	PWM_SetCompare1(Angle / 180 * 2000 + 500); // PA0
}

void Servo2_SetAngle(float Angle)
{
	// 限制角度范围到0-180度
	if(Angle < 0) Angle = 0;
	if(Angle > 180) Angle = 180;
	PWM_SetCompare2(Angle / 180 * 2000 + 500); // PA1
}

// 360度连续转动舵机控制函数
void Servo1_SetAngle360(float Angle)
{
	// 先尝试使用标准舵机控制方式进行测试
	// 将角度限制在0-180度范围内
	while(Angle < 0) Angle += 180;
	while(Angle >= 180) Angle -= 180;

	// 使用标准舵机的PWM范围：500-2500us
	uint16_t pwm_value = (uint16_t)(Angle / 180 * 2000 + 500);
	PWM_SetCompare1(pwm_value); // PA0
}

void Servo2_SetAngle360(float Angle)
{
	// 先尝试使用标准舵机控制方式进行测试
	// 将角度限制在0-180度范围内
	while(Angle < 0) Angle += 180;
	while(Angle >= 180) Angle -= 180;

	// 使用标准舵机的PWM范围：500-2500us
	uint16_t pwm_value = (uint16_t)(Angle / 180 * 2000 + 500);
	PWM_SetCompare2(pwm_value); // PA1
}