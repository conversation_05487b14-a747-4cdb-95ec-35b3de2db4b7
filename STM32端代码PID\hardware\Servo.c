#include "stm32f10x.h"                  // Device header
#include "PWM.h"
void Servo_Init(void)
{
	PWM_Init();
}

// 标准舵机控制函数（0-180度）
void Servo1_SetAngle(float Angle)
{
	// 限制角度范围到0-180度
	if(Angle < 0) Angle = 0;
	if(Angle > 180) Angle = 180;
	PWM_SetCompare1(Angle / 180 * 2000 + 500); // PA0
}

void Servo2_SetAngle(float Angle)
{
	// 限制角度范围到0-180度
	if(Angle < 0) Angle = 0;
	if(Angle > 180) Angle = 180;
	PWM_SetCompare2(Angle / 180 * 2000 + 500); // PA1
}

// 360度连续转动舵机控制函数
void Servo1_SetAngle360(float Angle)
{
	// 将360度角度映射到PWM值
	// 对于360度舵机，通常500-2500us对应360度
	while(Angle < 0) Angle += 360;
	while(Angle >= 360) Angle -= 360;

	uint16_t pwm_value = (uint16_t)(Angle / 360 * 2000 + 500);
	PWM_SetCompare1(pwm_value); // PA0
}

void Servo2_SetAngle360(float Angle)
{
	// 将360度角度映射到PWM值
	while(Angle < 0) Angle += 360;
	while(Angle >= 360) Angle -= 360;

	uint16_t pwm_value = (uint16_t)(Angle / 360 * 2000 + 500);
	PWM_SetCompare2(pwm_value); // PA1
}