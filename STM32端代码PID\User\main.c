#include "stm32f10x.h"// Device header
#include "Delay.h"
#include "OLED.h"
#include "PWM.h"  
#include "Serial.h"
#include "Servo.h"

// 舵机角度变量
float Angle1 = 80.0;  // 舵机1初始角度（水平方向）
float Angle2 = 80.0;  // 舵机2初始角度（垂直方向）

// 跟踪状态变量
uint8_t tracking_status = 0;  // 0:未跟踪 1:正在跟踪
uint8_t target_found = 0;     // 目标检测状态

// 外环（角度）PID参数
float Kp_angle = 0.6;
float Ki_angle = 0.01;
float Kd_angle = 0.2;
// 内环（速度）PID参数
float Kp_speed = 0.4;
float Ki_speed = 0.005;
float Kd_speed = 0.05;

// 外环PID变量
float last_error_angle1 = 0, integral_angle1 = 0;
float last_error_angle2 = 0, integral_angle2 = 0;
// 内环PID变量
float last_error_speed1 = 0, integral_speed1 = 0;
float last_error_speed2 = 0, integral_speed2 = 0;

// 速度变量
float Speed1 = 0, Speed2 = 0;

// 外环PID函数（角度）
float PID_Angle(float target, float current, float *last_error, float *integral)
{
    float error = target - current;
    *integral += error;
    float derivative = error - *last_error;
    float output = Kp_angle * error + Ki_angle * (*integral) + Kd_angle * derivative;
    *last_error = error;
    return output;
}
// 内环PID函数（速度）
float PID_Speed(float target, float current, float *last_error, float *integral)
{
    float error = target - current;
    *integral += error;
    float derivative = error - *last_error;
    float output = Kp_speed * error + Ki_speed * (*integral) + Kd_speed * derivative;
    *last_error = error;
    return output;
}

int main(void)
{
    Serial_Init();
    OLED_Init();
    Servo_Init();
    Servo1_SetAngle(Angle1);
    Servo2_SetAngle(Angle2);
    Delay_ms(1000);
    OLED_ShowString(1, 1, "Color Tracking");
    OLED_ShowString(2, 1, "Status: Waiting");
    OLED_ShowString(3, 1, "Angle1:");
    OLED_ShowString(4, 1, "Angle2:");
    while(1)
    {
        if(Serial_GetRxFlag() == 1)
        {
            if(pRxPacket >= 2)
            {
                float target_angle1, target_angle2;
                if(Serial_RxPacket[0] == 128 && Serial_RxPacket[1] == 128)
                {
                    target_found = 0;
                    tracking_status = 0;
                    OLED_ShowString(2, 8, "No Target");
                    target_angle1 = 90.0;
                    target_angle2 = 90.0;
                }
                else
                {
                    target_found = 1;
                    tracking_status = 1;
                    OLED_ShowString(2, 8, "Tracking ");
                    target_angle1 = Angle1 - (Serial_RxPacket[0] - 128) * 0.6;
                    target_angle2 = Angle2 - (Serial_RxPacket[1] - 128) * 0.6;
                }
                // 外环PID：角度环，输出目标速度
                float target_speed1 = PID_Angle(target_angle1, Angle1, &last_error_angle1, &integral_angle1);
                float target_speed2 = PID_Angle(target_angle2, Angle2, &last_error_angle2, &integral_angle2);
                // 内环PID：速度环，输出角度增量
                float output1 = PID_Speed(target_speed1, Speed1, &last_error_speed1, &integral_speed1);
                float output2 = PID_Speed(target_speed2, Speed2, &last_error_speed2, &integral_speed2);
                // 更新速度（简单积分，实际可用编码器反馈）
                Speed1 += output1;
                Speed2 += output2;
                // 更新角度
                Angle1 += Speed1;
                Angle2 += Speed2;
                if(Angle1 < 0) Angle1 = 0;
                if(Angle1 > 180) Angle1 = 180;
                if(Angle2 < 0) Angle2 = 0;
                if(Angle2 > 180) Angle2 = 180;
                Servo1_SetAngle(Angle1);
                Servo2_SetAngle(Angle2);
                OLED_ShowSignedNum(3, 8, Serial_RxPacket[0]-128, 3);
                OLED_ShowSignedNum(4, 8, Serial_RxPacket[1]-128, 3);
            }
        }
        OLED_ShowNum(3, 13, (int)Angle1, 3);
        OLED_ShowNum(4, 13, (int)Angle2, 3);
        Delay_ms(10);
    }
}
