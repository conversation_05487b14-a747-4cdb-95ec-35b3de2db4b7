#include "stm32f10x.h"// Device header
#include "Delay.h"
#include "OLED.h"
#include "PWM.h"  
#include "Serial.h"
#include "Servo.h"

// 舵机角度变量（支持360度连续转动）
float Angle1 = 90.0;  // 舵机1初始角度（水平方向）
float Angle2 = 90.0;  // 舵机2初始角度（垂直方向）

// 跟踪状态变量
uint8_t tracking_status = 0;  // 0:未跟踪 1:正在跟踪
uint8_t target_found = 0;     // 目标检测状态

// 优化的PID参数 - 单环PID控制
float Kp = 1.2;    // 比例系数，增加响应速度
float Ki = 0.02;   // 积分系数，减少稳态误差
float Kd = 0.3;    // 微分系数，减少超调

// 积分限幅参数
float integral_limit = 50.0;

// PID控制变量
float last_error1 = 0, integral1 = 0;
float last_error2 = 0, integral2 = 0;

// 目标丢失计数器
uint16_t target_lost_count = 0;
#define TARGET_LOST_THRESHOLD 100  // 目标丢失阈值

// 调试计数器
uint32_t debug_counter = 0;
uint32_t packet_count = 0;

// 优化的PID控制函数
float PID_Control(float error, float *last_error, float *integral)
{
    // 积分项累加
    *integral += error;

    // 积分限幅，防止积分饱和
    if(*integral > integral_limit) *integral = integral_limit;
    if(*integral < -integral_limit) *integral = -integral_limit;

    // 微分项计算
    float derivative = error - *last_error;

    // PID输出计算
    float output = Kp * error + Ki * (*integral) + Kd * derivative;

    // 更新上次误差
    *last_error = error;

    // 输出限幅
    if(output > 30.0) output = 30.0;
    if(output < -30.0) output = -30.0;

    return output;
}

int main(void)
{
    Serial_Init();
    OLED_Init();
    Servo_Init();
    Servo1_SetAngle360(Angle1);
    Servo2_SetAngle360(Angle2);
    Delay_ms(1000);
    OLED_ShowString(1, 1, "Color Tracking");
    OLED_ShowString(2, 1, "Status: Waiting");
    OLED_ShowString(3, 1, "Angle1:");
    OLED_ShowString(4, 1, "Angle2:");
    while(1)
    {
        if(Serial_GetRxFlag() == 1)
        {
            packet_count++;  // 数据包计数
            if(pRxPacket >= 2)
            {
                if(Serial_RxPacket[0] == 128 && Serial_RxPacket[1] == 128)
                {
                    // 目标丢失
                    target_found = 0;
                    target_lost_count++;
                    OLED_ShowString(2, 8, "No Target");

                    // 如果目标丢失时间过长，停止跟踪
                    if(target_lost_count > TARGET_LOST_THRESHOLD)
                    {
                        tracking_status = 0;
                        // 清零积分项，避免积分饱和
                        integral1 = 0;
                        integral2 = 0;
                    }
                }
                else
                {
                    // 目标找到
                    target_found = 1;
                    tracking_status = 1;
                    target_lost_count = 0;
                    OLED_ShowString(2, 8, "Tracking ");

                    // 计算误差（OpenMV发送的是误差值，128为中心点）
                    float error1 = (float)(Serial_RxPacket[0] - 128);  // 水平误差
                    float error2 = (float)(Serial_RxPacket[1] - 128);  // 垂直误差

                    // PID控制计算角度调整量
                    float angle_adjust1 = PID_Control(error1, &last_error1, &integral1);
                    float angle_adjust2 = PID_Control(error2, &last_error2, &integral2);

                    // 更新舵机角度
                    Angle1 += angle_adjust1;
                    Angle2 += angle_adjust2;

                    // 角度范围处理（支持360度连续转动）
                    while(Angle1 < 0) Angle1 += 360;
                    while(Angle1 >= 360) Angle1 -= 360;
                    while(Angle2 < 0) Angle2 += 360;
                    while(Angle2 >= 360) Angle2 -= 360;

                    // 设置舵机角度（支持360度连续转动）
                    Servo1_SetAngle360(Angle1);
                    Servo2_SetAngle360(Angle2);

                    // 显示误差值
                    OLED_ShowSignedNum(3, 8, (int)error1, 3);
                    OLED_ShowSignedNum(4, 8, (int)error2, 3);
                }
            }
        }
        // 显示当前角度和调试信息
        OLED_ShowNum(3, 13, (int)Angle1, 3);
        OLED_ShowNum(4, 13, (int)Angle2, 3);

        // 每1000次循环显示一次数据包计数（约10秒）
        debug_counter++;
        if(debug_counter >= 1000)
        {
            debug_counter = 0;
            // 可以在这里添加串口调试输出
            // Serial_Printf("Packets: %d, Status: %d\r\n", packet_count, tracking_status);
        }

        Delay_ms(10);
    }
}
