<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>MSPM0G507_ColorTracking</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.ti.ccstudio.core.ccsprojectnature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>driverlib</name>
			<type>2</type>
			<locationURI>MSPM0_SDK_INSTALL_DIR/source/driverlib</locationURI>
		</link>
		<link>
			<name>device</name>
			<type>2</type>
			<locationURI>MSPM0_SDK_INSTALL_DIR/source/ti/devices/msp/m0p</locationURI>
		</link>
	</linkedResources>
</projectDescription>
