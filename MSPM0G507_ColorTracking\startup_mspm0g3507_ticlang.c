/*
 * MSPM0G3507 启动文件
 * 适用于TI Clang编译器
 */

#include <stdint.h>

// 栈顶地址 (SRAM末尾)
#define STACK_TOP 0x20008000

// 外部函数声明
extern int main(void);
extern void UART_INST_IRQHandler(void);
extern void TIMER_INST_IRQHandler(void);

// 默认中断处理函数
void Default_Handler(void) {
    while(1);
}

// 系统异常处理函数
void Reset_Handler(void);
void NMI_Handler(void) __attribute__((weak, alias("Default_Handler")));
void HardFault_Handler(void) __attribute__((weak, alias("Default_Handler")));
void SVC_Handler(void) __attribute__((weak, alias("Default_Handler")));
void PendSV_Handler(void) __attribute__((weak, alias("Default_Handler")));
void SysTick_Handler(void) __attribute__((weak, alias("Default_Handler")));

// 外设中断处理函数
void UART0_IRQHandler(void) __attribute__((weak, alias("UART_INST_IRQHandler")));
void TIMG1_IRQHandler(void) __attribute__((weak, alias("TIMER_INST_IRQHandler")));

// 中断向量表
__attribute__((section(".intvecs")))
const void* vector_table[] = {
    (void*)STACK_TOP,           // 0: 栈顶指针
    Reset_Handler,              // 1: 复位处理函数
    NMI_Handler,                // 2: NMI
    HardFault_Handler,          // 3: 硬件错误
    0,                          // 4: 保留
    0,                          // 5: 保留
    0,                          // 6: 保留
    0,                          // 7: 保留
    0,                          // 8: 保留
    0,                          // 9: 保留
    0,                          // 10: 保留
    SVC_Handler,                // 11: SVCall
    0,                          // 12: 保留
    0,                          // 13: 保留
    PendSV_Handler,             // 14: PendSV
    SysTick_Handler,            // 15: SysTick
    
    // 外设中断 (16-31)
    Default_Handler,            // 16: GROUP0
    Default_Handler,            // 17: GROUP1
    Default_Handler,            // 18: TIMG8
    Default_Handler,            // 19: UART3
    Default_Handler,            // 20: ADC0_INT
    Default_Handler,            // 21: ADC1_INT
    Default_Handler,            // 22: CANFD0_INT
    Default_Handler,            // 23: DAC0_INT
    Default_Handler,            // 24: SPI0_INT
    Default_Handler,            // 25: SPI1_INT
    Default_Handler,            // 26: Reserved
    Default_Handler,            // 27: UART1_INT
    Default_Handler,            // 28: UART2_INT
    Default_Handler,            // 29: TIMG0_INT
    TIMG1_IRQHandler,           // 30: TIMG1_INT
    Default_Handler,            // 31: TIMG2_INT
    
    // 更多外设中断 (32-47)
    Default_Handler,            // 32: TIMG6_INT
    Default_Handler,            // 33: TIMG7_INT
    Default_Handler,            // 34: TIMG12_INT
    Default_Handler,            // 35: TIMG14_INT
    Default_Handler,            // 36: TIMG15_INT
    Default_Handler,            // 37: TIMG16_INT
    Default_Handler,            // 38: TIMG4_INT
    Default_Handler,            // 39: TIMG5_INT
    Default_Handler,            // 40: TIMG9_INT
    Default_Handler,            // 41: TIMG10_INT
    Default_Handler,            // 42: TIMG11_INT
    Default_Handler,            // 43: TIMG13_INT
    Default_Handler,            // 44: I2C0_INT
    Default_Handler,            // 45: I2C1_INT
    Default_Handler,            // 46: I2C2_INT
    UART0_IRQHandler,           // 47: UART0_INT
};

// 复位处理函数
void Reset_Handler(void) {
    // 初始化BSS段
    extern uint32_t __bss_start__;
    extern uint32_t __bss_end__;
    uint32_t *bss_ptr = &__bss_start__;
    while (bss_ptr < &__bss_end__) {
        *bss_ptr++ = 0;
    }
    
    // 初始化数据段
    extern uint32_t __data_load__;
    extern uint32_t __data_start__;
    extern uint32_t __data_end__;
    uint32_t *data_load_ptr = &__data_load__;
    uint32_t *data_ptr = &__data_start__;
    while (data_ptr < &__data_end__) {
        *data_ptr++ = *data_load_ptr++;
    }
    
    // 调用main函数
    main();
    
    // 如果main返回，进入无限循环
    while(1);
}
