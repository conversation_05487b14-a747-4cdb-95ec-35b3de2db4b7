/*
 * MSPM0G507 二维云台蓝色色块跟踪系统
 * 适用于CCS编译环境
 * 作者：AI Assistant
 * 日期：2025-01-24
 */

#include "ti_msp_dl_config.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// 系统配置
#define UART_BAUDRATE           115200
#define PWM_FREQUENCY           50      // 50Hz for servo control
#define TIMER_PERIOD_MS         10      // 10ms control loop

// 舵机角度变量（支持360度连续转动）
float Angle1 = 90.0f;  // 舵机1初始角度（水平方向）
float Angle2 = 90.0f;  // 舵机2初始角度（垂直方向）

// 跟踪状态变量
uint8_t tracking_status = 0;  // 0:未跟踪 1:正在跟踪
uint8_t target_found = 0;     // 目标检测状态

// 优化的PID参数 - 单环PID控制
float Kp = 1.2f;    // 比例系数，增加响应速度
float Ki = 0.02f;   // 积分系数，减少稳态误差
float Kd = 0.3f;    // 微分系数，减少超调

// 积分限幅参数
float integral_limit = 50.0f;

// PID控制变量
float last_error1 = 0, integral1 = 0;
float last_error2 = 0, integral2 = 0;

// 目标丢失计数器
uint16_t target_lost_count = 0;
#define TARGET_LOST_THRESHOLD 100  // 目标丢失阈值

// 串口接收相关变量
uint8_t uart_rx_data;
uint8_t uart_rx_packet[10];
uint8_t packet_index = 0;
uint8_t packet_ready = 0;
uint8_t rx_state = 0;  // 0: 等待包头, 1: 接收数据

// 调试计数器
uint32_t debug_counter = 0;
uint32_t packet_count = 0;

// 函数声明
void System_Init(void);
void PWM_Init(void);
void UART_Init(void);
void Timer_Init(void);
void Servo_SetAngle(uint8_t servo_num, float angle);
void Servo_SetAngle360(uint8_t servo_num, float angle);
float PID_Control(float error, float *last_error, float *integral);
void Process_UART_Data(void);

// 优化的PID控制函数
float PID_Control(float error, float *last_error, float *integral)
{
    // 积分项累加
    *integral += error;
    
    // 积分限幅，防止积分饱和
    if(*integral > integral_limit) *integral = integral_limit;
    if(*integral < -integral_limit) *integral = -integral_limit;
    
    // 微分项计算
    float derivative = error - *last_error;
    
    // PID输出计算
    float output = Kp * error + Ki * (*integral) + Kd * derivative;
    
    // 更新上次误差
    *last_error = error;
    
    // 输出限幅
    if(output > 30.0f) output = 30.0f;
    if(output < -30.0f) output = -30.0f;
    
    return output;
}

// 舵机角度设置函数（0-180度）
void Servo_SetAngle(uint8_t servo_num, float angle)
{
    // 限制角度范围到0-180度
    if(angle < 0) angle = 0;
    if(angle > 180) angle = 180;
    
    // 计算PWM占空比 (1ms-2ms对应0-180度)
    uint32_t pulse_width = (uint32_t)(1000 + (angle / 180.0f) * 1000); // 微秒
    uint32_t compare_value = (pulse_width * DL_TIMER_getCaptureCompareValue(PWM_TIMER_INST, DL_TIMER_CC_0_INDEX)) / 20000;
    
    if(servo_num == 1) {
        DL_TimerG_setCaptureCompareValue(PWM_TIMER_INST, compare_value, DL_TIMER_CC_0_INDEX);
    } else if(servo_num == 2) {
        DL_TimerG_setCaptureCompareValue(PWM_TIMER_INST, compare_value, DL_TIMER_CC_1_INDEX);
    }
}

// 舵机360度连续转动控制函数
void Servo_SetAngle360(uint8_t servo_num, float angle)
{
    // 角度范围处理（支持360度连续转动）
    while(angle < 0) angle += 360;
    while(angle >= 360) angle -= 360;
    
    // 对于360度舵机，映射到0-180度范围
    float mapped_angle = (angle / 360.0f) * 180.0f;
    Servo_SetAngle(servo_num, mapped_angle);
}

// 处理串口接收数据
void Process_UART_Data(void)
{
    if(packet_ready && packet_index >= 2)
    {
        packet_count++;  // 数据包计数
        
        if(uart_rx_packet[0] == 128 && uart_rx_packet[1] == 128)
        {
            // 目标丢失
            target_found = 0;
            target_lost_count++;
            
            // 如果目标丢失时间过长，停止跟踪
            if(target_lost_count > TARGET_LOST_THRESHOLD)
            {
                tracking_status = 0;
                // 清零积分项，避免积分饱和
                integral1 = 0;
                integral2 = 0;
            }
        }
        else
        {
            // 目标找到
            target_found = 1;
            tracking_status = 1;
            target_lost_count = 0;
            
            // 计算误差（OpenMV发送的是误差值，128为中心点）
            float error1 = (float)(uart_rx_packet[0] - 128);  // 水平误差
            float error2 = (float)(uart_rx_packet[1] - 128);  // 垂直误差
            
            // PID控制计算角度调整量
            float angle_adjust1 = PID_Control(error1, &last_error1, &integral1);
            float angle_adjust2 = PID_Control(error2, &last_error2, &integral2);
            
            // 更新舵机角度
            Angle1 += angle_adjust1;
            Angle2 += angle_adjust2;
            
            // 角度范围处理（支持360度连续转动）
            while(Angle1 < 0) Angle1 += 360;
            while(Angle1 >= 360) Angle1 -= 360;
            while(Angle2 < 0) Angle2 += 360;
            while(Angle2 >= 360) Angle2 -= 360;
            
            // 设置舵机角度（支持360度连续转动）
            Servo_SetAngle360(1, Angle1);
            Servo_SetAngle360(2, Angle2);
        }
        
        packet_ready = 0;  // 清除数据包就绪标志
    }
}

// UART中断处理函数
void UART_INST_IRQHandler(void)
{
    switch (DL_UART_getPendingInterrupt(UART_INST)) {
        case DL_UART_IIDX_RX:
            uart_rx_data = DL_UART_receiveData(UART_INST);

            // 数据包解析状态机
            if(rx_state == 0) {
                // 等待包头 0xFF
                if(uart_rx_data == 0xFF) {
                    rx_state = 1;
                    packet_index = 0;
                }
            } else if(rx_state == 1) {
                // 接收数据或包尾
                if(uart_rx_data == 0xFE) {
                    // 包尾，数据包接收完成
                    packet_ready = 1;
                    rx_state = 0;
                } else {
                    // 数据字节
                    if(packet_index < sizeof(uart_rx_packet)) {
                        uart_rx_packet[packet_index++] = uart_rx_data;
                    }
                }
            }
            break;
        default:
            break;
    }
}

// 定时器中断处理函数
void TIMER_INST_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMER_INST)) {
        case DL_TIMER_IIDX_ZERO:
            // 10ms定时中断，可用于系统定时任务
            break;
        default:
            break;
    }
}

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();

    // 设置舵机初始位置
    Servo_SetAngle360(1, Angle1);
    Servo_SetAngle360(2, Angle2);

    // 延时1秒等待系统稳定
    DL_Common_delayCycles(32000000);  // 1s @ 32MHz

    // 启用全局中断
    NVIC_EnableIRQ(UART_INST_INT_IRQN);
    NVIC_EnableIRQ(TIMER_INST_INT_IRQN);

    while(1)
    {
        // 处理串口接收的数据
        Process_UART_Data();

        // 调试计数器
        debug_counter++;
        if(debug_counter >= 1000)
        {
            debug_counter = 0;
            // 可以在这里添加调试输出
            // 切换LED状态作为心跳指示
            DL_GPIO_togglePins(LED_PORT, LED_PIN);
        }

        // 延时10ms
        DL_Common_delayCycles(320000);  // 10ms @ 32MHz
    }
}
