# MSPM0G507 Color Tracking System Makefile
# 适用于CCS命令行编译

# 编译器设置
CC = tiarmclang
LD = tiarmclang

# 目标器件
DEVICE = MSPM0G3507

# SDK路径 (需要根据实际安装路径修改)
SDK_PATH = C:/ti/mspm0_sdk_1_30_01_03

# 编译选项
CFLAGS = -mcpu=cortex-m0plus -march=armv6-m -mthumb -mfloat-abi=soft
CFLAGS += -I$(SDK_PATH)/source
CFLAGS += -I$(SDK_PATH)/source/ti/devices/msp/m0p
CFLAGS += -I$(SDK_PATH)/source/ti/devices/msp/m0p/inc
CFLAGS += -I$(SDK_PATH)/source/ti/driverlib/dl_common
CFLAGS += -I.
CFLAGS += -D__MSPM0G3507__
CFLAGS += -O2 -g

# 链接选项
LDFLAGS = -mcpu=cortex-m0plus -march=armv6-m -mthumb -mfloat-abi=soft
LDFLAGS += -T mspm0g3507.cmd
LDFLAGS += -L$(SDK_PATH)/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x
LDFLAGS += -l:driverlib.a
LDFLAGS += --entry ResetISR

# 源文件
SOURCES = main.c ti_msp_dl_config.c startup_mspm0g3507_ticlang.c

# 目标文件
OBJECTS = $(SOURCES:.c=.o)

# 目标名称
TARGET = MSPM0G507_ColorTracking

# 默认目标
all: $(TARGET).out

# 编译规则
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# 链接规则
$(TARGET).out: $(OBJECTS)
	$(LD) $(LDFLAGS) $(OBJECTS) -o $@

# 清理
clean:
	rm -f *.o *.out *.map

# 烧录 (需要安装uniflash)
flash: $(TARGET).out
	uniflash.sh -ccxml $(DEVICE).ccxml -program $<

.PHONY: all clean flash
