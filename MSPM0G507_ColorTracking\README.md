# MSPM0G507 二维云台蓝色色块跟踪系统

## 项目简介
本项目是基于TI MSPM0G507微控制器的二维云台蓝色色块跟踪系统，从STM32F103平台移植而来。系统通过OpenMV摄像头识别蓝色色块，并控制两个舵机实现360度跟踪功能。

## 主要特性
- ✅ 蓝色色块实时识别和跟踪
- ✅ 360度连续转动支持
- ✅ 优化的PID控制算法
- ✅ 串口通信协议
- ✅ 目标丢失检测和恢复
- ✅ 低功耗设计
- ✅ CCS开发环境支持

## 硬件要求

### 主控板
- TI MSPM0G507 开发板
- 或兼容的MSPM0G3507芯片

### 外设
- OpenMV摄像头 (H7/H7 Plus推荐)
- 2个舵机 (支持360度连续转动)
- 5V外部电源 (舵机供电)
- 连接线若干

### 引脚连接
```
MSPM0G507    →    外设
PA0          →    舵机1信号线 (水平)
PA1          →    舵机2信号线 (垂直)
PA10         →    OpenMV RX
PA11         →    OpenMV TX
PA27         →    状态LED
GND          →    公共地
```

## 软件环境

### 开发工具
- Code Composer Studio (CCS) 12.4+
- TI MSPM0 SDK 1.30+
- TI ARM Clang Compiler

### 依赖库
- MSPM0 DriverLib
- TI ARM Runtime Support

## 快速开始

### 1. 环境准备
1. 安装CCS开发环境
2. 下载并安装MSPM0 SDK
3. 配置SDK路径

### 2. 导入项目
1. 打开CCS
2. File → Import → CCS Projects
3. 选择项目目录
4. 导入项目

### 3. 编译项目
```bash
# 使用CCS GUI编译
Project → Build Project

# 或使用命令行编译
make all
```

### 4. 烧录程序
```bash
# 使用CCS烧录
Run → Debug

# 或使用命令行烧录
make flash
```

### 5. 运行测试
1. 连接硬件
2. 上电启动
3. 观察LED心跳指示
4. 测试舵机响应
5. 运行OpenMV代码
6. 测试色块跟踪

## 配置说明

### PID参数调整
在 `main.c` 中修改PID参数：
```c
float Kp = 1.2f;    // 比例系数 - 控制响应速度
float Ki = 0.02f;   // 积分系数 - 消除稳态误差  
float Kd = 0.3f;    // 微分系数 - 减少超调
```

### 舵机类型配置
```c
// 标准舵机 (0-180度)
Servo_SetAngle(servo_num, angle);

// 连续转动舵机 (360度)
Servo_SetAngle360(servo_num, angle);
```

### 通信参数
```c
#define UART_BAUDRATE    115200    // 波特率
#define PWM_FREQUENCY    50        // PWM频率 (Hz)
```

## 文件结构
```
MSPM0G507_ColorTracking/
├── main.c                  # 主程序文件
├── ti_msp_dl_config.h      # 系统配置头文件
├── ti_msp_dl_config.c      # 系统配置实现
├── mspm0g3507.cmd          # 链接脚本
├── Makefile                # 编译脚本
├── .project                # Eclipse项目文件
├── .ccsproject             # CCS项目配置
├── README.md               # 项目说明
└── 移植说明.md             # 详细移植文档
```

## 性能指标

| 指标 | 数值 |
|------|------|
| 控制频率 | 100Hz |
| 通信波特率 | 115200 |
| 角度分辨率 | 1° |
| 响应时间 | <50ms |
| 稳态误差 | <2° |
| 功耗 | <10mA (不含舵机) |

## 故障排除

### 常见问题
1. **舵机不响应**
   - 检查PWM信号输出
   - 确认舵机电源供应
   - 验证引脚连接

2. **通信异常**
   - 检查串口连接
   - 确认波特率设置
   - 验证数据包格式

3. **跟踪不稳定**
   - 调整PID参数
   - 检查机械结构
   - 优化光照条件

### 调试方法
1. 使用CCS调试器设置断点
2. 观察LED心跳指示
3. 监控串口数据
4. 检查PWM波形

## 贡献指南
欢迎提交Issue和Pull Request来改进项目。

## 许可证
本项目采用MIT许可证，详见LICENSE文件。

## 联系方式
如有问题请提交Issue或联系项目维护者。

---
**注意**：使用前请仔细阅读移植说明文档，确保硬件连接正确。
