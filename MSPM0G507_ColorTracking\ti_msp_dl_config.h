/*
 * MSPM0G507 配置头文件
 * 定义了系统时钟、GPIO、PWM、UART等配置
 */

#ifndef TI_MSP_DL_CONFIG_H
#define TI_MSP_DL_CONFIG_H

#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

// 系统时钟配置
#define CPUCLK_FREQ                     32000000
#define POWER_STARTUP_DELAY             16

// PWM配置 - 用于舵机控制
#define PWM_TIMER_INST                  TIMG0
#define PWM_TIMER_INST_IRQHandler       TIMG0_IRQHandler
#define PWM_TIMER_INST_INT_IRQN         TIMG0_INT_IRQn
#define PWM_TIMER_CLOCK_SOURCE          DL_TIMER_CLOCK_BUSCLK
#define PWM_TIMER_CLOCK_DIVIDER         DL_TIMER_CLOCK_DIVIDE_8
#define PWM_TIMER_PERIOD                20000  // 20ms period for 50Hz

// PWM引脚配置
#define PWM_SERVO1_PORT                 GPIOA
#define PWM_SERVO1_PIN                  DL_GPIO_PIN_0
#define PWM_SERVO1_IOMUX                (IOMUX_PINCM1)
#define PWM_SERVO1_IOMUX_FUNC           IOMUX_PINCM1_PF_TIMG0_CCP0

#define PWM_SERVO2_PORT                 GPIOA
#define PWM_SERVO2_PIN                  DL_GPIO_PIN_1
#define PWM_SERVO2_IOMUX                (IOMUX_PINCM2)
#define PWM_SERVO2_IOMUX_FUNC           IOMUX_PINCM2_PF_TIMG0_CCP1

// UART配置 - 用于与OpenMV通信
#define UART_INST                       UART0
#define UART_INST_IRQHandler            UART0_IRQHandler
#define UART_INST_INT_IRQN              UART0_INT_IRQn
#define UART_INST_CLOCK_SOURCE          DL_UART_CLOCK_BUSCLK

// UART引脚配置
#define UART_RX_PORT                    GPIOA
#define UART_RX_PIN                     DL_GPIO_PIN_11
#define UART_RX_IOMUX                   (IOMUX_PINCM22)
#define UART_RX_IOMUX_FUNC              IOMUX_PINCM22_PF_UART0_RX

#define UART_TX_PORT                    GPIOA
#define UART_TX_PIN                     DL_GPIO_PIN_10
#define UART_TX_IOMUX                   (IOMUX_PINCM21)
#define UART_TX_IOMUX_FUNC              IOMUX_PINCM21_PF_UART0_TX

// 定时器配置 - 用于系统定时
#define TIMER_INST                      TIMG1
#define TIMER_INST_IRQHandler           TIMG1_IRQHandler
#define TIMER_INST_INT_IRQN             TIMG1_INT_IRQn
#define TIMER_INST_CLOCK_SOURCE         DL_TIMER_CLOCK_BUSCLK
#define TIMER_INST_CLOCK_DIVIDER        DL_TIMER_CLOCK_DIVIDE_8
#define TIMER_INST_PERIOD               40000  // 10ms @ 4MHz (32MHz/8)

// LED配置 - 用于状态指示
#define LED_PORT                        GPIOA
#define LED_PIN                         DL_GPIO_PIN_27

// 函数声明
void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_init(void);
void SYSCFG_DL_UART_init(void);
void SYSCFG_DL_TIMER_init(void);

#endif /* TI_MSP_DL_CONFIG_H */
