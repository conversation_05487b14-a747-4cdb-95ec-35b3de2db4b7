# 云台不跟随色块转动 - 故障排查指南

## 问题现象
- OLED显示正常
- 云台不跟随色块转动

## 排查步骤

### 1. 检查串口通信
**目标**：确认STM32是否正确接收到OpenMV的数据

**检查方法**：
- 观察OLED第一行右侧是否显示数字（我已添加调试代码显示接收到的原始数据）
- 正常情况下应该看到两个数字在变化
- 如果没有目标时应该显示 128 128
- 有目标时数字应该在1-255之间变化

**如果数据不变化**：
1. 检查串口连接：OpenMV TX → STM32 PB11, OpenMV RX → STM32 PB10
2. 检查共地连接
3. 确认OpenMV代码正在运行

### 2. 检查舵机硬件连接
**目标**：确认舵机硬件连接正确

**检查方法**：
1. **电源连接**：
   - 舵机红线 → 5V电源（不要接STM32的5V，电流不够）
   - 舵机黑线 → 电源地 + STM32地
   - 舵机信号线 → STM32 PA0(舵机1), PA1(舵机2)

2. **舵机类型确认**：
   - 确认使用的是标准舵机（0-180度）还是连续转动舵机
   - 我已修改代码先使用标准舵机模式测试

### 3. 测试舵机基本功能
**目标**：验证舵机是否能响应PWM信号

**测试代码**（在main函数while循环前添加）：
```c
// 舵机测试代码
for(int i = 0; i < 3; i++)
{
    Servo1_SetAngle(0);
    Servo2_SetAngle(0);
    Delay_ms(1000);
    Servo1_SetAngle(90);
    Servo2_SetAngle(90);
    Delay_ms(1000);
    Servo1_SetAngle(180);
    Servo2_SetAngle(180);
    Delay_ms(1000);
}
```

**预期结果**：舵机应该在0度、90度、180度之间摆动

### 4. 检查PWM信号输出
**目标**：确认STM32是否输出正确的PWM信号

**检查方法**：
1. 用示波器测量PA0、PA1的PWM信号
2. 正常PWM参数：
   - 频率：50Hz（周期20ms）
   - 脉宽：0.5ms-2.5ms（对应0-180度）

**如果没有示波器**：
- 用LED测试：将LED正极接PA0，负极接地，应该看到LED闪烁

### 5. 检查角度计算逻辑
**目标**：确认角度计算是否正确

**观察OLED显示**：
- 第3行：水平误差值
- 第4行：垂直误差值
- 第3行右侧：当前水平角度
- 第4行右侧：当前垂直角度

**正常现象**：
- 当色块在图像中心时，误差值应该接近0
- 当色块偏离中心时，误差值应该为正值或负值
- 角度值应该根据误差值变化

### 6. 简化测试
我已经修改了代码，使用简化的控制逻辑：
- 当误差 > 5时，角度增加2度
- 当误差 < -5时，角度减少2度
- 使用标准舵机函数而不是360度函数

## 常见问题及解决方案

### 问题1：舵机不动
**可能原因**：
- 舵机供电不足
- PWM信号线接触不良
- 舵机损坏

**解决方案**：
- 使用外部5V电源给舵机供电
- 检查信号线连接
- 更换舵机测试

### 问题2：舵机抖动
**可能原因**：
- PWM信号不稳定
- 电源纹波过大
- PID参数不合适

**解决方案**：
- 检查电源质量
- 降低PID参数
- 添加滤波电容

### 问题3：舵机转动方向错误
**可能原因**：
- 误差计算方向错误
- 舵机安装方向错误

**解决方案**：
- 修改误差计算的正负号
- 调整舵机安装方向

### 问题4：数据接收异常
**可能原因**：
- 串口连接错误
- 波特率不匹配
- 数据包格式错误

**解决方案**：
- 检查串口连接
- 确认波特率为115200
- 检查OpenMV发送的数据格式

## 调试建议

1. **逐步测试**：
   - 先测试舵机硬件
   - 再测试串口通信
   - 最后测试整体功能

2. **观察OLED显示**：
   - 确认数据接收正常
   - 观察角度变化
   - 检查状态显示

3. **使用简化代码**：
   - 先用我修改的简化控制逻辑
   - 确认基本功能后再使用PID控制

4. **记录测试结果**：
   - 记录每个测试步骤的结果
   - 便于定位具体问题

按照这个指南逐步排查，应该能找到问题所在。
