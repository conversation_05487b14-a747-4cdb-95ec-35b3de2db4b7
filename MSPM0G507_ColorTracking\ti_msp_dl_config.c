/*
 * MSPM0G507 系统配置实现文件
 * 包含时钟、GPIO、PWM、UART等初始化函数
 */

#include "ti_msp_dl_config.h"

// 系统初始化
void SYSCFG_DL_init(void)
{
    SYSCFG_DL_initPower();
    SYSCFG_DL_SYSCTL_init();
    SYSCFG_DL_GPIO_init();
    SYSCFG_DL_PWM_init();
    SYSCFG_DL_UART_init();
    SYSCFG_DL_TIMER_init();
}

// 电源管理初始化
void SYSCFG_DL_initPower(void)
{
    DL_GPIO_reset(GPIOA);
    DL_TimerG_reset(PWM_TIMER_INST);
    DL_UART_reset(UART_INST);
    DL_TimerG_reset(TIMER_INST);

    DL_GPIO_enablePower(GPIOA);
    DL_TimerG_enablePower(PWM_TIMER_INST);
    DL_UART_enablePower(UART_INST);
    DL_TimerG_enablePower(TIMER_INST);
    
    DL_Common_delayCycles(POWER_STARTUP_DELAY);
}

// 系统时钟初始化
void SYSCFG_DL_SYSCTL_init(void)
{
    DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
    DL_SYSCTL_setMCLKSource(SYSOSC, HSCLK, DL_SYSCTL_MCLK_DIVIDER_DISABLE);
}

// GPIO初始化
void SYSCFG_DL_GPIO_init(void)
{
    // PWM引脚配置
    DL_GPIO_initPeripheralOutputFunction(PWM_SERVO1_IOMUX, PWM_SERVO1_IOMUX_FUNC);
    DL_GPIO_initPeripheralOutputFunction(PWM_SERVO2_IOMUX, PWM_SERVO2_IOMUX_FUNC);
    
    // UART引脚配置
    DL_GPIO_initPeripheralOutputFunction(UART_TX_IOMUX, UART_TX_IOMUX_FUNC);
    DL_GPIO_initPeripheralInputFunction(UART_RX_IOMUX, UART_RX_IOMUX_FUNC);
    
    // LED引脚配置
    DL_GPIO_initDigitalOutput(LED_PORT, LED_PIN);
    DL_GPIO_clearPins(LED_PORT, LED_PIN);
    DL_GPIO_enableOutput(LED_PORT, LED_PIN);
}

// PWM初始化 - 用于舵机控制
void SYSCFG_DL_PWM_init(void)
{
    DL_TimerG_ClockConfig clockConfig = {
        .clockSel = PWM_TIMER_CLOCK_SOURCE,
        .divideRatio = PWM_TIMER_CLOCK_DIVIDER,
        .prescale = 0U
    };

    DL_TimerG_initPWMMode(PWM_TIMER_INST, &clockConfig, PWM_TIMER_PERIOD);
    
    // 配置PWM通道0 (舵机1)
    DL_TimerG_setCaptureCompareOutCtl(PWM_TIMER_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
        DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
        DL_TIMERG_CAPTURE_COMPARE_0_INDEX);
    
    DL_TimerG_setCaptureCompareValue(PWM_TIMER_INST, 1500, DL_TIMERG_CAPTURE_COMPARE_0_INDEX); // 1.5ms初始脉宽
    
    // 配置PWM通道1 (舵机2)
    DL_TimerG_setCaptureCompareOutCtl(PWM_TIMER_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
        DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
        DL_TIMERG_CAPTURE_COMPARE_1_INDEX);
    
    DL_TimerG_setCaptureCompareValue(PWM_TIMER_INST, 1500, DL_TIMERG_CAPTURE_COMPARE_1_INDEX); // 1.5ms初始脉宽
    
    DL_TimerG_enableClock(PWM_TIMER_INST);
    DL_TimerG_startCounter(PWM_TIMER_INST);
}

// UART初始化 - 用于与OpenMV通信
void SYSCFG_DL_UART_init(void)
{
    DL_UART_ClockConfig clockConfig = {
        .clockSel = UART_INST_CLOCK_SOURCE,
        .divideRatio = DL_UART_CLOCK_DIVIDE_RATIO_1
    };

    DL_UART_Config uartConfig = {
        .mode = DL_UART_MODE_NORMAL,
        .direction = DL_UART_DIRECTION_TX_RX,
        .flowControl = DL_UART_FLOW_CONTROL_NONE,
        .parity = DL_UART_PARITY_NONE,
        .wordLength = DL_UART_WORD_LENGTH_8_BITS,
        .stopBits = DL_UART_STOP_BITS_ONE
    };

    DL_UART_init(UART_INST, &clockConfig, &uartConfig);
    DL_UART_configBaudRate(UART_INST, CPUCLK_FREQ, 115200);
    
    // 启用接收中断
    DL_UART_enableInterrupt(UART_INST, DL_UART_INTERRUPT_RX);
    
    DL_UART_enable(UART_INST);
}

// 定时器初始化 - 用于系统定时
void SYSCFG_DL_TIMER_init(void)
{
    DL_TimerG_ClockConfig clockConfig = {
        .clockSel = TIMER_INST_CLOCK_SOURCE,
        .divideRatio = TIMER_INST_CLOCK_DIVIDER,
        .prescale = 0U
    };

    DL_TimerG_initTimerMode(TIMER_INST, &clockConfig, TIMER_INST_PERIOD);
    
    // 启用定时器中断
    DL_TimerG_enableInterrupt(TIMER_INST, DL_TIMER_INTERRUPT_ZERO_EVENT);
    
    DL_TimerG_enableClock(TIMER_INST);
    DL_TimerG_startCounter(TIMER_INST);
}

// PWM初始化函数
void PWM_Init(void)
{
    // PWM已在SYSCFG_DL_PWM_init()中初始化
}

// UART初始化函数
void UART_Init(void)
{
    // UART已在SYSCFG_DL_UART_init()中初始化
}

// 定时器初始化函数
void Timer_Init(void)
{
    // Timer已在SYSCFG_DL_TIMER_init()中初始化
}
