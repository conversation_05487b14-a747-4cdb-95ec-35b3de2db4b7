#include "stm32f10x.h"                  // Device header
#include <stdio.h>
#include <stdarg.h>
uint8_t Serial_RxData;
uint8_t Serial_RxFlag;


uint8_t Serial_TxPacket[10];
uint8_t Serial_RxPacket[10];
uint8_t pRxPacket = 0;

void Serial_Init(void)
{
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3,ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
	
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10 ;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure);
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11 ;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB,&GPIO_InitStructure);

	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate = 115200;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Tx |USART_Mode_Rx;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	
	USART_Init(USART3,&USART_InitStructure);
	
	USART_ITConfig(USART3,USART_IT_RXNE,ENABLE);
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
	NVIC_InitTypeDef NVIC_InitStructure;
	NVIC_InitStructure.NVIC_IRQChannel =USART3_IRQn ;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
	NVIC_Init(&NVIC_InitStructure);
	USART_Cmd(USART3,ENABLE);
	
	
	

}

void Serial_SendByte(uint8_t Byte)
{
	USART_SendData(USART3,Byte);
	while(USART_GetFlagStatus(USART3,USART_FLAG_TXE) == RESET);
}
void Serial_SendArray(uint8_t *Array, uint8_t Length)
{
	int i;
	for( i = 0;i < Length ;i++){
		Serial_SendByte(Array[i]);
	}
}
void Serial_SendString(char *String)
{
	for(int i = 0;String[i] != 0;i++){
		Serial_SendByte(String[i]);
	}
}
uint32_t Serial_Pow(uint32_t X,uint32_t Y)
{
	uint32_t result = 1;
	while(Y --)
	{
		result *= X;
	}
	return result;
	
}
void Serial_SendNumber(uint32_t Number, uint8_t Length)
{
	uint8_t i;
	uint8_t Byte;
	for(i = 0; i < Length;i++)
	{
		Byte = (Number / Serial_Pow(10,Length-i-1))%10;
		Serial_SendByte(Byte + 0x30);	
	}	
}
int fputc(int ch,FILE *f)  //printf底层函数重映射
{
	Serial_SendByte(ch);
	return ch;
}
void Serial_Printf(char *format, ...)
{
	char String[100];
	va_list arg;
	va_start(arg, format);
	vsprintf(String,format,arg);
	va_end(arg);
	Serial_SendString(String);
}
uint8_t Serial_GetRxFlag(void)
{
	if(Serial_RxFlag == 1)
	{
		Serial_RxFlag = 0;
		return 1;
	}
	return 0;
}
uint8_t Serial_GetRxData(void)
{
	return Serial_RxData;
}
void USART3_IRQHandler(void)
{
		static uint8_t Rxstatus = 0;
	if(USART_GetITStatus(USART3,USART_IT_RXNE) == SET)
	{
		Serial_RxData = USART_ReceiveData(USART3);
		if(Rxstatus == 0)
		{
			if(Serial_RxData == 0xFF){
				Rxstatus = 1;
				pRxPacket = 0;
			}
		}
		else if(Rxstatus == 1)
		{
			if(Serial_RxData == 0xFE){
				Serial_RxFlag = 1;
				USART_ClearITPendingBit(USART3,USART_IT_RXNE);
				Rxstatus = 0;	
			}
			else
			{
				Serial_RxPacket[pRxPacket] = Serial_RxData;
				pRxPacket ++;
				
			}
			
		}
		USART_ClearITPendingBit(USART3,USART_IT_RXNE);
		
	}
}

	
	
