# MSPM0G507 二维云台跟踪系统移植说明

## 概述
本项目将STM32F103的二维云台蓝色色块跟踪系统移植到TI MSPM0G507平台，适用于CCS编译环境。

## 硬件连接

### 1. 舵机连接
- **舵机1（水平）**：PA0 (TIMG0_CCP0)
- **舵机2（垂直）**：PA1 (TIMG0_CCP1)
- **舵机电源**：外部5V电源（不要用MCU供电）
- **舵机地线**：与MCU共地

### 2. OpenMV连接
- **UART RX**：PA11 (UART0_RX) ← OpenMV TX
- **UART TX**：PA10 (UART0_TX) → OpenMV RX
- **共地连接**：GND

### 3. 状态指示
- **LED**：PA27 (用于心跳指示)

## 软件架构

### 主要功能模块
1. **PWM控制**：使用TIMG0产生50Hz PWM信号控制舵机
2. **UART通信**：使用UART0接收OpenMV数据，115200波特率
3. **PID控制**：单环PID算法，支持积分限幅
4. **中断处理**：UART接收中断和定时器中断

### 关键特性
- 支持360度连续转动舵机
- 数据包协议解析（0xFF包头，0xFE包尾）
- 目标丢失检测和处理
- 积分饱和防护

## CCS项目配置

### 1. 创建新项目
1. 打开CCS，选择 File → New → CCS Project
2. 选择 MSPM0G3507 作为目标器件
3. 选择 Empty Project 模板
4. 项目名称：MSPM0G507_ColorTracking

### 2. 导入文件
将以下文件复制到项目目录：
- `main.c` - 主程序文件
- `ti_msp_dl_config.h` - 配置头文件
- `ti_msp_dl_config.c` - 配置实现文件
- `mspm0g3507.cmd` - 链接脚本
- `.project` - 项目配置文件
- `.ccsproject` - CCS项目配置

### 3. 配置编译选项
在项目属性中设置：
- **Compiler**：TI Clang Compiler
- **Include Paths**：添加MSPM0 SDK路径
- **Linker File**：使用 mspm0g3507.cmd

### 4. SDK依赖
确保安装了MSPM0 SDK，并在CCS中正确配置SDK路径。

## 代码说明

### 主要函数
```c
// PID控制函数
float PID_Control(float error, float *last_error, float *integral);

// 舵机控制函数
void Servo_SetAngle(uint8_t servo_num, float angle);        // 0-180度
void Servo_SetAngle360(uint8_t servo_num, float angle);     // 360度连续

// 数据处理函数
void Process_UART_Data(void);

// 中断处理函数
void UART_INST_IRQHandler(void);    // UART接收中断
void TIMER_INST_IRQHandler(void);   // 定时器中断
```

### PID参数
```c
float Kp = 1.2f;    // 比例系数
float Ki = 0.02f;   // 积分系数
float Kd = 0.3f;    // 微分系数
float integral_limit = 50.0f;  // 积分限幅
```

### 通信协议
- **数据包格式**：0xFF + 数据 + 0xFE
- **数据内容**：[X误差+128, Y误差+128]
- **目标丢失**：[128, 128]

## 调试建议

### 1. 硬件测试
1. 检查舵机是否能正常转动到初始位置（90度）
2. 使用示波器检查PWM信号（50Hz，1-2ms脉宽）
3. 检查UART通信是否正常

### 2. 软件调试
1. 在UART中断中设置断点，检查数据接收
2. 观察LED心跳指示是否正常
3. 调整PID参数以获得最佳跟踪效果

### 3. 常见问题
- **舵机不动**：检查PWM信号和电源
- **通信异常**：检查波特率和引脚连接
- **跟踪不稳定**：调整PID参数

## 性能对比

| 特性 | STM32F103 | MSPM0G507 |
|------|-----------|-----------|
| 主频 | 72MHz | 32MHz |
| Flash | 64KB | 128KB |
| RAM | 20KB | 32KB |
| 功耗 | 较高 | 超低功耗 |
| 成本 | 中等 | 较低 |

## 扩展功能

### 可能的改进
1. **低功耗模式**：利用MSPM0的低功耗特性
2. **更多传感器**：添加IMU、编码器等
3. **无线通信**：集成WiFi或蓝牙模块
4. **机器学习**：边缘AI算法优化

## 注意事项

1. **时钟配置**：确保系统时钟配置正确（32MHz）
2. **中断优先级**：合理设置中断优先级
3. **电源管理**：注意舵机电源供应
4. **调试接口**：保留SWD调试接口

本移植版本保持了原有的所有功能，并针对MSPM0G507的特性进行了优化。
